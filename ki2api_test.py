#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ki2API端点测试脚本
测试URL: https://muskwoled-ki2api.hf.space/
API Key: ki2api-key-2024
"""

import requests
import json
import time

class Ki2APITester:
    def __init__(self):
        self.base_url = "https://muskwoled-ki2api.hf.space"
        self.api_key = "ki2api-key-2024"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "Ki2API-Tester/1.0"
        }

    def test_health_endpoint(self):
        """测试健康检查端点"""
        print("🔍 测试健康检查端点...")
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False

    def test_models_endpoint(self):
        """测试模型列表端点"""
        print("\n🤖 测试模型列表端点...")
        try:
            response = requests.get(
                f"{self.base_url}/v1/models", 
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"可用模型数量: {len(data.get('data', []))}")
                for model in data.get('data', [])[:3]:  # 显示前3个模型
                    print(f"  - {model.get('id', 'Unknown')}")
            else:
                print(f"错误响应: {response.text}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 模型列表获取失败: {e}")
            return False

    def test_chat_endpoint(self):
        """测试聊天端点"""
        print("\n💬 测试聊天端点...")
        
        payload = {
            "model": "claude-3-5-sonnet-20241022",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello! Please respond with a simple greeting."
                }
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        try:
            print(f"请求URL: {self.base_url}/v1/chat/completions")
            print(f"请求头: {json.dumps(dict(self.headers), indent=2)}")
            print(f"请求体: {json.dumps(payload, indent=2)}")
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            print(f"\n响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                if "choices" in data and len(data["choices"]) > 0:
                    content = data["choices"][0]["message"]["content"]
                    print(f"✅ AI回复: {content}")
                    return True
            else:
                print(f"❌ 聊天请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 聊天请求异常: {e}")
            return False

    def test_different_models(self):
        """测试不同的模型名称"""
        print("\n🔄 测试不同模型名称...")
        
        models_to_test = [
            "claude-3-5-sonnet-20241022",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307",
            "gpt-4",
            "gpt-3.5-turbo"
        ]
        
        for model in models_to_test:
            print(f"\n测试模型: {model}")
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": "Hi"}],
                "max_tokens": 50
            }
            
            try:
                response = requests.post(
                    f"{self.base_url}/v1/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=15
                )
                print(f"  状态码: {response.status_code}")
                if response.status_code != 200:
                    print(f"  错误: {response.text[:200]}")
                else:
                    print(f"  ✅ 成功")
            except Exception as e:
                print(f"  ❌ 异常: {e}")

    def test_with_different_headers(self):
        """测试不同的请求头"""
        print("\n🔧 测试不同请求头...")
        
        header_variants = [
            # 标准Bearer token
            {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"},
            # 不带Bearer前缀
            {"Authorization": self.api_key, "Content-Type": "application/json"},
            # 使用x-api-key
            {"x-api-key": self.api_key, "Content-Type": "application/json"},
            # 使用api-key
            {"api-key": self.api_key, "Content-Type": "application/json"},
        ]
        
        payload = {
            "model": "claude-3-5-sonnet-20241022",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 50
        }
        
        for i, headers in enumerate(header_variants, 1):
            print(f"\n测试请求头变体 {i}: {headers}")
            try:
                response = requests.post(
                    f"{self.base_url}/v1/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=15
                )
                print(f"  状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"  ✅ 成功!")
                    return headers
                else:
                    print(f"  错误: {response.text[:100]}")
            except Exception as e:
                print(f"  ❌ 异常: {e}")
        
        return None

    def test_base_url_variants(self):
        """测试不同的URL变体"""
        print("\n🌐 测试URL变体...")
        
        url_variants = [
            "https://muskwoled-ki2api.hf.space",
            "https://muskwoled-ki2api.hf.space/",
            "https://muskwoled-ki2api.hf.space/v1",
        ]
        
        for url in url_variants:
            print(f"\n测试URL: {url}")
            try:
                # 测试根路径
                response = requests.get(url, timeout=10)
                print(f"  根路径状态码: {response.status_code}")
                
                # 测试聊天端点
                chat_url = f"{url.rstrip('/')}/v1/chat/completions"
                payload = {
                    "model": "claude-3-5-sonnet-20241022",
                    "messages": [{"role": "user", "content": "Hi"}],
                    "max_tokens": 50
                }
                
                response = requests.post(
                    chat_url,
                    headers=self.headers,
                    json=payload,
                    timeout=15
                )
                print(f"  聊天端点状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"  ✅ 找到工作的URL: {url}")
                    return url
                    
            except Exception as e:
                print(f"  ❌ 异常: {e}")
        
        return None

    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🚀 开始Ki2API全面测试")
        print("=" * 60)
        print(f"基础URL: {self.base_url}")
        print(f"API Key: {self.api_key}")
        print("=" * 60)
        
        # 1. 基础连通性测试
        health_ok = self.test_health_endpoint()
        
        # 2. 模型列表测试
        models_ok = self.test_models_endpoint()
        
        # 3. 基础聊天测试
        chat_ok = self.test_chat_endpoint()
        
        # 4. 如果基础测试失败，进行深度诊断
        if not chat_ok:
            print("\n🔧 基础聊天测试失败，开始深度诊断...")
            
            # 测试不同请求头
            working_headers = self.test_with_different_headers()
            
            # 测试不同URL
            working_url = self.test_base_url_variants()
            
            # 测试不同模型
            self.test_different_models()
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        print(f"健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
        print(f"模型列表: {'✅ 通过' if models_ok else '❌ 失败'}")
        print(f"聊天功能: {'✅ 通过' if chat_ok else '❌ 失败'}")
        
        if not chat_ok:
            print("\n🔍 可能的问题:")
            print("1. API Key无效或格式错误")
            print("2. 模型名称不正确")
            print("3. 请求头格式问题")
            print("4. 服务端配置问题")
            print("5. 网络连接问题")

def main():
    tester = Ki2APITester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
