# MCP服务器配置修复指南

## 🔍 问题诊断

**当前配置**:
```json
"mcp-feedback-enhanced": {
  "command": "uvx",
  "args": ["mcp-feedback-enhanced@latest"],
  "env": {
    "TIMEOUT": "600"
  }
}
```

**问题原因**:
- `uvx` 是临时运行命令，每次都重新安装
- 重新安装时遇到pywin32 wheel文件损坏问题
- 我们已经成功安装了工具，但配置没有使用它

## ✅ 解决方案

### 方案1：使用已安装的工具（强烈推荐）

**修改配置为**:
```json
"mcp-feedback-enhanced": {
  "command": "C:\\Users\\<USER>\\.local\\bin\\mcp-feedback-enhanced.exe",
  "args": ["server"],
  "env": {
    "TIMEOUT": "600"
  }
}
```

**优势**:
- ✅ 使用已安装的稳定版本
- ✅ 启动速度快（无需重新安装）
- ✅ 避免网络和缓存问题
- ✅ 更可靠和稳定

### 方案2：修复uvx配置

如果必须使用uvx，修改配置为：
```json
"mcp-feedback-enhanced": {
  "command": "uvx",
  "args": ["--link-mode", "copy", "mcp-feedback-enhanced@latest"],
  "env": {
    "TIMEOUT": "600",
    "UV_LINK_MODE": "copy",
    "UV_CACHE_DIR": "%TEMP%\\uv_cache_new"
  }
}
```

### 方案3：使用PATH中的命令

如果PATH已正确设置：
```json
"mcp-feedback-enhanced": {
  "command": "mcp-feedback-enhanced",
  "args": ["server"],
  "env": {
    "TIMEOUT": "600",
    "PATH": "C:\\Users\\<USER>\\.local\\bin;%PATH%"
  }
}
```

## 🔧 配置文件位置

常见的MCP配置文件位置：
- **Claude Desktop**: `%APPDATA%\Claude\claude_desktop_config.json`
- **VS Code**: `.vscode/settings.json`
- **其他IDE**: 查看具体IDE的MCP配置文档

## 📋 完整修复步骤

### 1. 确认工具已安装
```powershell
# 检查工具是否存在
ls "C:\Users\<USER>\.local\bin\mcp-feedback-enhanced.exe"

# 测试工具是否工作
C:\Users\<USER>\.local\bin\mcp-feedback-enhanced.exe --help
```

### 2. 备份当前配置
```powershell
# 备份配置文件
Copy-Item "配置文件路径" "配置文件路径.backup"
```

### 3. 修改配置文件
使用文本编辑器打开配置文件，将mcp-feedback-enhanced部分替换为推荐配置。

### 4. 重启应用
重启使用MCP的应用程序以加载新配置。

### 5. 验证配置
检查MCP服务器是否正常启动。

## 🎯 验证安装

### 测试命令
```powershell
# 测试帮助信息
C:\Users\<USER>\.local\bin\mcp-feedback-enhanced.exe --help

# 测试版本信息
C:\Users\<USER>\.local\bin\mcp-feedback-enhanced.exe version

# 测试服务器启动（会启动服务器，按Ctrl+C停止）
C:\Users\<USER>\.local\bin\mcp-feedback-enhanced.exe server
```

### 预期输出
```
usage: mcp-feedback-enhanced [-h] {server,test,version} ...

MCP Feedback Enhanced Enhanced - 互動式回饋收集 MCP 伺服器

positional arguments:
  {server,test,version}
                        可用命令
    server              啟動 MCP 伺服器（預設）
    test                執行測試
    version             顯示版本資訊
```

## 🛡️ 故障排除

### 常见问题

#### 1. 找不到命令
**症状**: 'mcp-feedback-enhanced' is not recognized
**解决**: 使用完整路径或添加到PATH

#### 2. 权限被拒绝
**症状**: Access denied
**解决**: 以管理员身份运行或检查文件权限

#### 3. 配置语法错误
**症状**: JSON解析错误
**解决**: 检查JSON语法，确保引号和逗号正确

#### 4. 服务器启动失败
**症状**: Connection closed
**解决**: 检查端口占用，确保没有其他实例运行

## 📊 配置对比

| 方案 | 启动速度 | 稳定性 | 网络依赖 | 推荐度 |
|------|----------|--------|----------|--------|
| 已安装工具 | ⚡ 很快 | ✅ 很高 | ❌ 无 | ⭐⭐⭐⭐⭐ |
| 修复uvx | 🐌 较慢 | ⚠️ 中等 | ✅ 需要 | ⭐⭐⭐ |
| PATH命令 | ⚡ 快 | ✅ 高 | ❌ 无 | ⭐⭐⭐⭐ |

## 🎉 推荐配置

**最终推荐配置**:
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "C:\\Users\\<USER>\\.local\\bin\\mcp-feedback-enhanced.exe",
      "args": ["server"],
      "env": {
        "TIMEOUT": "600"
      }
    }
  }
}
```

**为什么推荐这个配置**:
1. 🚀 **快速启动** - 无需重新安装
2. 🛡️ **稳定可靠** - 避免网络和缓存问题
3. 🔧 **易于维护** - 明确的文件路径
4. 📈 **性能优秀** - 最小的资源消耗

## 🏆 总结

**问题**: uvx配置导致重复安装和pywin32错误  
**解决**: 使用已安装的工具而不是临时运行  
**结果**: ✅ MCP服务器稳定快速启动  

修改配置后，MCP服务器应该能够正常启动，不再出现pywin32相关的错误！
