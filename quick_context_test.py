#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速上下文测试脚本
用于快速测试指定长度的上下文
"""

import requests
import json
import time

def quick_context_test(model="deepseek-v3-0324", target_chars=100000):
    """快速测试指定长度的上下文"""
    
    api_key = "sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59"
    url = "https://www.chatfreely.xyz/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 生成测试文本
    base_text = "这是测试文本片段，用于验证模型的上下文处理能力。包含中文字符以模拟真实使用场景。"
    base_len = len(base_text)
    repeat_count = (target_chars // base_len) + 1
    
    # 生成带编号的文本
    text_parts = []
    for i in range(repeat_count):
        numbered_text = f"[第{i+1:04d}段] {base_text}"
        text_parts.append(numbered_text)
    
    full_text = "\n".join(text_parts)[:target_chars]
    
    # 估算token数
    chinese_chars = sum(1 for char in full_text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(full_text) - chinese_chars
    estimated_tokens = int(chinese_chars / 1.5 + other_chars / 4)
    
    print(f"🚀 快速上下文测试")
    print(f"🤖 模型: {model}")
    print(f"📏 目标长度: {target_chars:,} 字符")
    print(f"📊 估算tokens: {estimated_tokens:,}")
    print(f"📄 实际长度: {len(full_text):,} 字符")
    print("-" * 50)
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": f"{full_text}\n\n请回答：1) 你总共看到了多少段文本？2) 第一段和最后一段的编号分别是什么？3) 请简单总结内容。"
            }
        ],
        "max_tokens": 300,
        "temperature": 0.3
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=120)
        end_time = time.time()
        
        print(f"📡 状态码: {response.status_code}")
        print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 测试成功!")
            
            # 显示token使用情况
            if "usage" in data:
                usage = data["usage"]
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)
                
                print(f"\n📊 实际Token使用:")
                print(f"  输入tokens: {prompt_tokens:,}")
                print(f"  输出tokens: {completion_tokens:,}")
                print(f"  总计tokens: {total_tokens:,}")
                print(f"  估算准确度: {(estimated_tokens/prompt_tokens*100):.1f}%")
            
            # 显示AI回复
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"\n🤖 AI回复:")
                print(content)
            
            # 保存结果
            result = {
                "model": model,
                "target_chars": target_chars,
                "actual_chars": len(full_text),
                "estimated_tokens": estimated_tokens,
                "actual_tokens": usage if "usage" in data else None,
                "response_time": end_time - start_time,
                "success": True,
                "response": data
            }
            
            with open(f"context_test_{model}_{target_chars}.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 结果已保存到 context_test_{model}_{target_chars}.json")
            
        else:
            print(f"❌ 测试失败!")
            print(f"错误: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时 - 可能上下文太长")
    except Exception as e:
        print(f"💥 错误: {e}")

def test_multiple_lengths():
    """测试多个长度"""
    models = ["kimi-k2-instruct", "deepseek-v3-0324", "deepseek-r1-0528"]
    test_lengths = [10000, 50000, 100000, 200000, 500000]
    
    print("🔄 批量上下文长度测试")
    print("=" * 50)
    
    for model in models:
        print(f"\n🤖 测试模型: {model}")
        print("-" * 30)
        
        for length in test_lengths:
            print(f"\n📏 测试长度: {length:,} 字符")
            quick_context_test(model, length)
            
            # 如果失败就停止测试更大的长度
            time.sleep(2)  # 避免请求过于频繁

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) >= 3:
        model = sys.argv[1]
        length = int(sys.argv[2])
        quick_context_test(model, length)
    elif len(sys.argv) == 2:
        if sys.argv[1] == "batch":
            test_multiple_lengths()
        else:
            length = int(sys.argv[1])
            quick_context_test("deepseek-v3-0324", length)
    else:
        print("使用方法:")
        print("  python quick_context_test.py [长度]")
        print("  python quick_context_test.py [模型] [长度]") 
        print("  python quick_context_test.py batch")
        print("\n示例:")
        print("  python quick_context_test.py 100000")
        print("  python quick_context_test.py kimi-k2-instruct 50000")
        print("  python quick_context_test.py batch")
        
        # 默认测试
        quick_context_test()
