#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型上下文长度极限测试脚本
通过逐步增加输入长度来找到每个模型的实际上下文限制
"""

import requests
import json
import time
import math
from typing import Dict, List, Any, Tuple

class ContextLimitTester:
    def __init__(self):
        self.api_key = "sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59"
        self.base_url = "https://www.chatfreely.xyz/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 可用的模型列表（基于之前的测试结果）
        self.available_models = [
            "deepseek-r1-0528",
            "deepseek-v3-0324", 
            "kimi-k2-instruct"
        ]
        
        # 测试用的基础文本单元（约100个字符）
        self.base_text = "这是一个用于测试模型上下文长度的文本片段。它包含了中文字符，用来模拟真实的使用场景。每个片段大约包含一百个字符左右。"
        
    def generate_text_by_length(self, target_chars: int) -> str:
        """生成指定长度的测试文本"""
        base_len = len(self.base_text)
        repeat_count = math.ceil(target_chars / base_len)
        
        # 生成文本并添加序号标记
        text_parts = []
        for i in range(repeat_count):
            numbered_text = f"[片段{i+1}] {self.base_text}"
            text_parts.append(numbered_text)
        
        full_text = "\n".join(text_parts)
        
        # 截取到目标长度
        if len(full_text) > target_chars:
            full_text = full_text[:target_chars]
            
        return full_text
    
    def estimate_tokens(self, text: str) -> int:
        """粗略估算token数量（中文约1.5字符=1token，英文约4字符=1token）"""
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        # 中文字符按1.5:1计算，其他字符按4:1计算
        estimated_tokens = int(chinese_chars / 1.5 + other_chars / 4)
        return estimated_tokens
    
    def test_context_length(self, model: str, char_length: int) -> Dict[str, Any]:
        """测试指定长度的上下文"""
        test_text = self.generate_text_by_length(char_length)
        estimated_tokens = self.estimate_tokens(test_text)
        
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": f"{test_text}\n\n请总结上面的内容，并告诉我你看到了多少个片段编号。"
                }
            ],
            "max_tokens": 200,
            "temperature": 0.3
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            result = {
                "model": model,
                "input_chars": len(test_text),
                "estimated_input_tokens": estimated_tokens,
                "status_code": response.status_code,
                "response_time": round(end_time - start_time, 2),
                "success": False,
                "error": None,
                "actual_tokens": None,
                "response_content": None
            }
            
            if response.status_code == 200:
                data = response.json()
                result["success"] = True
                
                # 获取实际token使用情况
                if "usage" in data:
                    usage = data["usage"]
                    result["actual_tokens"] = {
                        "prompt_tokens": usage.get("prompt_tokens", 0),
                        "completion_tokens": usage.get("completion_tokens", 0),
                        "total_tokens": usage.get("total_tokens", 0)
                    }
                
                # 获取响应内容
                if "choices" in data and len(data["choices"]) > 0:
                    result["response_content"] = data["choices"][0]["message"]["content"]
                
                print(f"✅ {char_length:,} 字符 ({estimated_tokens:,} 估算tokens) - 成功")
                if result["actual_tokens"]:
                    actual_input = result["actual_tokens"]["prompt_tokens"]
                    print(f"   实际输入tokens: {actual_input:,}")
                
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ {char_length:,} 字符 - 失败: {result['error'][:100]}...")
                
        except requests.exceptions.Timeout:
            result["error"] = "请求超时"
            print(f"⏰ {char_length:,} 字符 - 超时")
        except Exception as e:
            result["error"] = f"异常: {str(e)}"
            print(f"💥 {char_length:,} 字符 - 异常: {e}")
            
        return result
    
    def binary_search_max_context(self, model: str) -> Dict[str, Any]:
        """使用二分查找法找到模型的最大上下文长度"""
        print(f"\n🔍 开始测试 {model} 的最大上下文长度")
        print("=" * 60)
        
        # 设置搜索范围（字符数）
        min_chars = 1000      # 最小1K字符
        max_chars = 2000000   # 最大2M字符（约130万tokens）
        
        successful_lengths = []
        failed_lengths = []
        test_results = []
        
        # 首先测试一些关键点
        test_points = [1000, 5000, 10000, 50000, 100000, 200000, 500000, 1000000]
        
        print("📊 预测试关键点...")
        for chars in test_points:
            if chars > max_chars:
                break
                
            result = self.test_context_length(model, chars)
            test_results.append(result)
            
            if result["success"]:
                successful_lengths.append(chars)
                min_chars = chars  # 更新下界
            else:
                failed_lengths.append(chars)
                max_chars = chars  # 更新上界
                break
            
            time.sleep(1)  # 避免请求过于频繁
        
        # 如果所有预测试都成功，继续测试更大的值
        if not failed_lengths:
            print("\n🚀 所有预测试都成功，继续测试更大的上下文...")
            large_test_points = [1500000, 2000000, 3000000, 5000000]
            
            for chars in large_test_points:
                result = self.test_context_length(model, chars)
                test_results.append(result)
                
                if result["success"]:
                    successful_lengths.append(chars)
                    min_chars = chars
                else:
                    failed_lengths.append(chars)
                    max_chars = chars
                    break
                    
                time.sleep(1)
        
        # 二分查找精确边界
        if successful_lengths and failed_lengths:
            print(f"\n🎯 二分查找精确边界 ({min_chars:,} - {max_chars:,} 字符)")
            
            iteration = 0
            while max_chars - min_chars > 10000 and iteration < 10:  # 精度到1万字符
                iteration += 1
                mid_chars = (min_chars + max_chars) // 2
                
                print(f"\n第{iteration}轮: 测试 {mid_chars:,} 字符")
                result = self.test_context_length(model, mid_chars)
                test_results.append(result)
                
                if result["success"]:
                    successful_lengths.append(mid_chars)
                    min_chars = mid_chars
                else:
                    failed_lengths.append(mid_chars)
                    max_chars = mid_chars
                
                time.sleep(1)
        
        # 计算最终结果
        max_successful_chars = max(successful_lengths) if successful_lengths else 0
        max_successful_tokens = 0
        
        if max_successful_chars > 0:
            # 找到最大成功测试的实际token数
            for result in test_results:
                if (result["success"] and 
                    result["input_chars"] == max_successful_chars and 
                    result["actual_tokens"]):
                    max_successful_tokens = result["actual_tokens"]["prompt_tokens"]
                    break
        
        summary = {
            "model": model,
            "max_context_chars": max_successful_chars,
            "max_context_tokens": max_successful_tokens,
            "successful_tests": len(successful_lengths),
            "failed_tests": len(failed_lengths),
            "total_tests": len(test_results),
            "test_results": test_results
        }
        
        return summary
    
    def run_all_context_tests(self):
        """运行所有模型的上下文测试"""
        print("🚀 开始模型上下文长度极限测试")
        print("=" * 80)
        
        all_summaries = []
        
        for model in self.available_models:
            try:
                summary = self.binary_search_max_context(model)
                all_summaries.append(summary)
                
                print(f"\n📋 {model} 测试完成:")
                print(f"   最大上下文: {summary['max_context_chars']:,} 字符")
                print(f"   最大tokens: {summary['max_context_tokens']:,}")
                print(f"   测试次数: {summary['total_tests']}")
                
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断了 {model} 的测试")
                break
            except Exception as e:
                print(f"\n💥 测试 {model} 时发生错误: {e}")
                continue
        
        # 生成最终报告
        self.generate_context_report(all_summaries)
        
        return all_summaries
    
    def generate_context_report(self, summaries: List[Dict[str, Any]]):
        """生成上下文测试报告"""
        print("\n" + "=" * 80)
        print("📊 上下文长度测试报告")
        print("=" * 80)
        
        # 按最大上下文排序
        summaries.sort(key=lambda x: x["max_context_chars"], reverse=True)
        
        print("\n🏆 模型上下文长度排名:")
        for i, summary in enumerate(summaries, 1):
            chars = summary["max_context_chars"]
            tokens = summary["max_context_tokens"]
            print(f"{i}. {summary['model']}")
            print(f"   最大上下文: {chars:,} 字符 ({tokens:,} tokens)")
            
            # 估算页数（按每页2000字符计算）
            pages = chars / 2000 if chars > 0 else 0
            print(f"   约等于: {pages:.1f} 页文档")
            print()
        
        # 保存详细结果
        with open("context_test_results.json", "w", encoding="utf-8") as f:
            json.dump(summaries, f, ensure_ascii=False, indent=2)
        
        print("💾 详细测试结果已保存到 context_test_results.json")

def main():
    """主函数"""
    tester = ContextLimitTester()
    
    print("🔍 模型上下文长度极限测试器")
    print(f"🔑 API Key: {tester.api_key[:20]}...{tester.api_key[-10:]}")
    print(f"🤖 待测试模型: {', '.join(tester.available_models)}")
    print("\n⚠️  注意: 此测试可能需要较长时间和较多API调用")
    
    try:
        input("\n按回车键开始测试...")
        results = tester.run_all_context_tests()
        print("\n🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
