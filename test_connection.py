#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ChatFreely.xyz连接性
"""

import asyncio
from playwright.async_api import async_playwright

async def test_connection():
    """测试网站连接"""
    print("🔍 测试ChatFreely.xyz连接性...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,  # 显示浏览器以观察Cloudflare
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            page = await context.new_page()
            
            print("📱 正在访问主页...")
            try:
                await page.goto("https://www.chatfreely.xyz", timeout=60000)
                print(f"✅ 主页访问成功! 标题: {await page.title()}")
                
                # 等待Cloudflare验证
                if "请稍候" in await page.title() or "Just a moment" in await page.title():
                    print("🛡️ 检测到Cloudflare保护，等待验证...")
                    await page.wait_for_timeout(15000)
                    
                    # 等待验证完成
                    for i in range(6):
                        current_title = await page.title()
                        print(f"⏳ 当前标题: {current_title}")
                        if "ChatFreely" in current_title:
                            print("✅ Cloudflare验证完成!")
                            break
                        await page.wait_for_timeout(5000)
                        print(f"⏳ 等待验证... ({i+1}/6)")
                
                # 尝试访问注册页面
                print("📝 正在访问注册页面...")
                await page.goto("https://www.chatfreely.xyz/register", timeout=60000)
                print(f"✅ 注册页面访问成功! 标题: {await page.title()}")
                
                # 检查页面内容
                await page.wait_for_timeout(3000)
                content = await page.content()
                
                if "用户名" in content:
                    print("✅ 注册表单加载成功!")
                else:
                    print("⚠️ 注册表单未找到")
                
                # 保持页面打开一段时间供观察
                print("🔍 页面将保持打开30秒供观察...")
                await page.wait_for_timeout(30000)
                
            except Exception as e:
                print(f"❌ 访问失败: {e}")
                
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_connection())
