# ChatFreely.xyz 自动注册机使用说明

## 🎯 功能特点

- ✅ **无需邮箱验证** - 完全自动化注册
- ✅ **自动生成账号** - 随机用户名和密码
- ✅ **批量注册** - 支持一次注册多个账号
- ✅ **API Key获取** - 自动尝试获取API密钥
- ✅ **账号管理** - 自动保存账号信息到JSON文件
- ✅ **错误处理** - 完善的异常处理机制

## 📋 注册要求分析

根据实际测试，ChatFreely.xyz的注册要求：

### 必填字段
- **用户名**: 2-24位字符
- **密码**: 6-36位字符  
- **确认密码**: 必须与密码一致

### 验证方式
- ❌ **无需邮箱** - 这是最大优势！
- ❌ **无需手机号** - 完全免验证
- ❌ **无需验证码** - 可能有Cloudflare保护

## 🚀 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements_register.txt
playwright install chromium
```

### 2. 运行注册机
```bash
python chatfreely_register.py
```

### 3. 选择操作模式

#### 模式1: 单个账号注册
- 可以指定用户名和密码
- 也可以留空自动生成
- 自动保存账号信息

#### 模式2: 批量账号注册  
- 指定注册数量（默认5个）
- 自动生成随机账号
- 每个账号间隔10-30秒避免频繁请求

#### 模式3: 登录获取API Key
- 使用已有账号登录
- 尝试自动获取API Key

## 📁 输出文件

### chatfreely_accounts.json
保存所有注册成功的账号信息：
```json
[
  {
    "success": true,
    "username": "abc123def",
    "password": "MyPassword123!",
    "api_key": "sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59",
    "url": "https://www.chatfreely.xyz/",
    "created_time": "2025-01-31 15:30:45"
  }
]
```

## ⚙️ 配置选项

### 浏览器设置
```python
# 修改 chatfreely_register.py 中的浏览器启动参数
browser = await p.chromium.launch(
    headless=True,  # True=无头模式，False=显示浏览器
    args=['--no-sandbox', '--disable-dev-shm-usage']
)
```

### 用户名生成规则
```python
def generate_username(self, length: int = 8) -> str:
    # 默认8位，字母+数字组合
    # 可以修改length参数调整长度
```

### 密码生成规则
```python
def generate_password(self, length: int = 12) -> str:
    # 默认12位，包含大小写字母、数字、特殊字符
    # 可以修改length参数调整长度
```

## 🛡️ 安全注意事项

### 1. 请求频率控制
- 批量注册时自动添加10-30秒随机延迟
- 避免触发反爬虫机制
- 建议单次批量不超过10个账号

### 2. Cloudflare保护
- 网站有Cloudflare保护
- 可能需要人工验证
- 建议使用非无头模式观察

### 3. 账号安全
- 生成的密码包含特殊字符
- 建议定期更换密码
- 不要在重要场合使用这些账号

## 🔧 故障排除

### 常见问题

#### 1. Playwright安装失败
```bash
# 手动安装浏览器
playwright install chromium
# 或者安装所有浏览器
playwright install
```

#### 2. 注册失败
- 检查网络连接
- 确认网站是否正常访问
- 尝试手动注册验证流程

#### 3. API Key获取失败
- 可能需要登录后手动查找
- 检查用户设置或API管理页面
- 有些网站需要额外申请API权限

#### 4. Cloudflare验证
- 设置headless=False观察验证过程
- 可能需要手动完成验证
- 尝试更换User-Agent

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 显示浏览器窗口
headless=False

# 增加等待时间
await page.wait_for_timeout(10000)
```

## 📊 成功率优化

### 提高成功率的建议

1. **合理的请求间隔**
   - 单次注册后等待30-60秒
   - 批量注册时随机化间隔时间

2. **模拟真实用户行为**
   - 随机鼠标移动
   - 模拟打字延迟
   - 随机页面停留时间

3. **多样化的账号信息**
   - 不同长度的用户名
   - 不同复杂度的密码
   - 避免明显的模式

4. **网络环境**
   - 使用稳定的网络连接
   - 考虑使用代理IP
   - 避免在高峰时段运行

## 🎯 使用建议

### 合理使用
- 仅用于学习和测试目的
- 遵守网站服务条款
- 不要进行恶意注册

### 最佳实践
- 先手动注册1-2个账号熟悉流程
- 小批量测试后再大规模使用
- 定期检查账号状态
- 备份重要的API Key

## 📞 技术支持

如果遇到问题：
1. 检查网站是否更新了注册流程
2. 更新Playwright到最新版本
3. 查看浏览器控制台错误信息
4. 尝试手动注册对比流程差异

---

**免责声明**: 此工具仅供学习和研究使用，请遵守相关网站的服务条款和法律法规。
