#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatFreely.xyz 自动注册机
无需邮箱验证，自动生成账号并获取API Key
"""

import asyncio
import random
import string
import time
import json
from playwright.async_api import async_playwright
from typing import Dict, Optional, List

class ChatFreelyRegister:
    def __init__(self):
        self.base_url = "https://www.chatfreely.xyz"
        self.api_url = "https://www.chatfreely.xyz/v1/chat/completions"
        
    def generate_username(self, length: int = 8) -> str:
        """生成随机用户名"""
        # 使用字母和数字组合
        chars = string.ascii_lowercase + string.digits
        username = ''.join(random.choice(chars) for _ in range(length))
        # 确保以字母开头
        if username[0].isdigit():
            username = random.choice(string.ascii_lowercase) + username[1:]
        return username
    
    def generate_password(self, length: int = 12) -> str:
        """生成随机密码"""
        # 包含大小写字母、数字和特殊字符
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))
    
    async def register_account(self, username: str = None, password: str = None) -> Dict:
        """注册新账号"""
        if not username:
            username = self.generate_username()
        if not password:
            password = self.generate_password()
            
        print(f"🚀 开始注册账号")
        print(f"👤 用户名: {username}")
        print(f"🔐 密码: {password}")
        print("-" * 50)
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 设为True可以无头模式运行
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            try:
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                page = await context.new_page()
                
                # 访问注册页面
                print("📱 正在访问注册页面...")
                await page.goto(f"{self.base_url}/register", wait_until="networkidle")
                
                # 等待页面加载
                await page.wait_for_timeout(3000)
                
                # 关闭可能的公告弹窗
                try:
                    close_btn = page.locator('button:has-text("我已知晓")')
                    if await close_btn.is_visible():
                        await close_btn.click()
                        await page.wait_for_timeout(1000)
                except:
                    pass
                
                # 填写注册表单 - 第一步
                print("📝 正在填写注册表单...")

                # 填写用户名
                username_input = page.locator('input[placeholder*="用户名"]')
                await username_input.fill(username)
                await page.wait_for_timeout(500)

                # 填写密码
                password_input = page.locator('input[placeholder*="密码"]').first
                await password_input.fill(password)
                await page.wait_for_timeout(500)

                # 确认密码
                confirm_password_input = page.locator('input[placeholder*="再次输入密码"]')
                await confirm_password_input.fill(password)
                await page.wait_for_timeout(500)

                # 点击下一步按钮
                print("✅ 正在提交第一步...")
                next_btn = page.locator('button:has-text("下一步")')
                await next_btn.click()

                # 等待第二步页面
                await page.wait_for_timeout(3000)

                # 填写注册表单 - 第二步（邮箱）
                print("📧 正在填写邮箱信息...")

                # 生成临时邮箱
                temp_email = f"{username}@temp.com"
                email_input = page.locator('input[placeholder*="邮箱"]')
                await email_input.fill(temp_email)
                await page.wait_for_timeout(500)

                # 点击注册按钮
                print("✅ 正在提交注册...")
                register_btn = page.locator('button:has-text("注册")')
                await register_btn.click()

                # 等待注册结果
                await page.wait_for_timeout(5000)
                
                # 检查是否注册成功
                current_url = page.url
                print(f"📍 当前页面: {current_url}")
                
                if "/login" in current_url or "登录" in await page.title():
                    print("❌ 注册可能失败，跳转到登录页面")
                    return {"success": False, "error": "注册失败"}
                
                # 尝试获取API Key
                api_key = await self.get_api_key(page)

                # 如果没有获取到API Key，尝试生成一个
                if not api_key:
                    print("🔑 正在生成API Key...")
                    api_key = await self.generate_api_key(page)
                
                result = {
                    "success": True,
                    "username": username,
                    "password": password,
                    "api_key": api_key,
                    "url": current_url
                }
                
                print("🎉 注册成功!")
                print(f"👤 用户名: {username}")
                print(f"🔐 密码: {password}")
                if api_key:
                    print(f"🔑 API Key: {api_key}")
                
                return result
                
            except Exception as e:
                print(f"💥 注册过程中发生错误: {e}")
                return {"success": False, "error": str(e)}
            
            finally:
                await browser.close()
    
    async def get_api_key(self, page) -> Optional[str]:
        """尝试获取API Key"""
        try:
            print("🔍 正在查找API Key...")

            # 点击用户名菜单
            try:
                user_btn = page.locator('button:has-text("testuser")')
                if not await user_btn.is_visible():
                    user_btn = page.locator('button[class*="user"], button[class*="avatar"]').first
                await user_btn.click()
                await page.wait_for_timeout(1000)
            except:
                pass

            # 点击API设置
            try:
                api_setting_btn = page.locator('text="API 设置"')
                if await api_setting_btn.is_visible():
                    await api_setting_btn.click()
                    await page.wait_for_timeout(2000)
            except:
                pass

            # 尝试多种方式查找API Key
            selectors = [
                'input[value*="sk-"]',
                'textbox[value*="sk-"]',
                'code:has-text("sk-")',
                'span:has-text("sk-")',
                'div:has-text("sk-")',
                '[data-key*="sk-"]',
                '.api-key',
                '#api-key'
            ]

            for selector in selectors:
                try:
                    element = page.locator(selector)
                    if await element.is_visible():
                        # 尝试获取value属性
                        value = await element.get_attribute('value')
                        if value and "sk-" in value:
                            import re
                            match = re.search(r'sk-[a-zA-Z0-9]{48,}', value)
                            if match:
                                return match.group(0)

                        # 尝试获取文本内容
                        text = await element.text_content()
                        if text and "sk-" in text:
                            import re
                            match = re.search(r'sk-[a-zA-Z0-9]{48,}', text)
                            if match:
                                return match.group(0)
                except:
                    continue

            # 尝试在页面源码中查找
            content = await page.content()
            import re
            match = re.search(r'sk-[a-zA-Z0-9]{48,}', content)
            if match:
                return match.group(0)

            print("⚠️ 未找到API Key，尝试生成新的")
            return None

        except Exception as e:
            print(f"🔍 获取API Key时出错: {e}")
            return None

    async def generate_api_key(self, page) -> Optional[str]:
        """生成新的API Key"""
        try:
            print("🔑 正在生成新的API Key...")

            # 确保在API设置页面
            try:
                user_btn = page.locator('button:has-text("testuser")')
                if not await user_btn.is_visible():
                    # 查找用户按钮的其他方式
                    user_btns = page.locator('button').all()
                    for btn in await user_btns:
                        text = await btn.text_content()
                        if text and any(char.isalpha() for char in text):
                            await btn.click()
                            break
                else:
                    await user_btn.click()
                await page.wait_for_timeout(1000)

                # 点击API设置
                api_setting_btn = page.locator('text="API 设置"')
                await api_setting_btn.click()
                await page.wait_for_timeout(2000)
            except Exception as e:
                print(f"⚠️ 无法打开API设置: {e}")

            # 点击重置密钥按钮
            reset_btn = page.locator('button:has-text("重置密钥")')
            if await reset_btn.is_visible():
                await reset_btn.click()
                await page.wait_for_timeout(1000)

                # 确认重置
                confirm_btn = page.locator('button:has-text("确认")')
                if await confirm_btn.is_visible():
                    await confirm_btn.click()
                    await page.wait_for_timeout(3000)

                    # 获取新生成的API Key
                    return await self.get_api_key(page)

            print("⚠️ 无法生成API Key")
            return None

        except Exception as e:
            print(f"🔑 生成API Key时出错: {e}")
            return None
    
    async def login_and_get_key(self, username: str, password: str) -> Optional[str]:
        """登录并获取API Key"""
        print(f"🔐 正在登录账号: {username}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            
            try:
                context = await browser.new_context()
                page = await context.new_page()
                
                # 访问登录页面
                await page.goto(f"{self.base_url}/login", wait_until="networkidle")
                await page.wait_for_timeout(3000)
                
                # 填写登录表单
                username_input = page.locator('input[placeholder*="用户名"]')
                await username_input.fill(username)
                
                password_input = page.locator('input[placeholder*="密码"]')
                await password_input.fill(password)
                
                # 点击登录
                login_btn = page.locator('button:has-text("登录")')
                await login_btn.click()
                
                await page.wait_for_timeout(5000)
                
                # 获取API Key
                api_key = await self.get_api_key(page)
                return api_key
                
            except Exception as e:
                print(f"💥 登录过程中发生错误: {e}")
                return None
            
            finally:
                await browser.close()
    
    def save_account(self, account_info: Dict):
        """保存账号信息到文件"""
        try:
            # 读取现有账号
            try:
                with open("chatfreely_accounts.json", "r", encoding="utf-8") as f:
                    accounts = json.load(f)
            except FileNotFoundError:
                accounts = []
            
            # 添加新账号
            account_info["created_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
            accounts.append(account_info)
            
            # 保存到文件
            with open("chatfreely_accounts.json", "w", encoding="utf-8") as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)
            
            print(f"💾 账号信息已保存到 chatfreely_accounts.json")
            
        except Exception as e:
            print(f"💥 保存账号信息时出错: {e}")
    
    async def batch_register(self, count: int = 5) -> List[Dict]:
        """批量注册账号"""
        print(f"🔄 开始批量注册 {count} 个账号")
        print("=" * 60)
        
        results = []
        
        for i in range(count):
            print(f"\n📋 正在注册第 {i+1}/{count} 个账号")
            
            try:
                result = await self.register_account()
                results.append(result)
                
                if result["success"]:
                    self.save_account(result)
                    print(f"✅ 第 {i+1} 个账号注册成功")
                else:
                    print(f"❌ 第 {i+1} 个账号注册失败: {result.get('error', '未知错误')}")
                
                # 避免请求过于频繁
                if i < count - 1:
                    wait_time = random.randint(10, 30)
                    print(f"⏰ 等待 {wait_time} 秒后继续...")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                print(f"💥 第 {i+1} 个账号注册时发生异常: {e}")
                results.append({"success": False, "error": str(e)})
        
        # 生成批量注册报告
        successful = [r for r in results if r.get("success")]
        failed = [r for r in results if not r.get("success")]
        
        print("\n" + "=" * 60)
        print("📊 批量注册报告")
        print("=" * 60)
        print(f"✅ 成功: {len(successful)} 个")
        print(f"❌ 失败: {len(failed)} 个")
        print(f"📈 成功率: {len(successful)/count*100:.1f}%")
        
        return results

async def main():
    """主函数"""
    register = ChatFreelyRegister()
    
    print("🤖 ChatFreely.xyz 自动注册机")
    print("=" * 50)
    print("1. 单个账号注册")
    print("2. 批量账号注册")
    print("3. 登录并获取API Key")
    
    choice = input("\n请选择操作 (1-3): ").strip()
    
    if choice == "1":
        # 单个注册
        username = input("用户名 (留空自动生成): ").strip() or None
        password = input("密码 (留空自动生成): ").strip() or None
        
        result = await register.register_account(username, password)
        if result["success"]:
            register.save_account(result)
        
    elif choice == "2":
        # 批量注册
        try:
            count = int(input("注册数量 (默认5): ").strip() or "5")
            await register.batch_register(count)
        except ValueError:
            print("❌ 请输入有效数字")
    
    elif choice == "3":
        # 登录获取Key
        username = input("用户名: ").strip()
        password = input("密码: ").strip()
        
        if username and password:
            api_key = await register.login_and_get_key(username, password)
            if api_key:
                print(f"🔑 API Key: {api_key}")
            else:
                print("❌ 未能获取API Key")
        else:
            print("❌ 用户名和密码不能为空")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
