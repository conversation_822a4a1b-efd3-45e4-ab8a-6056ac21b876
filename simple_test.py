#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版API测试脚本
快速测试单个模型
"""

import requests
import json

def quick_test():
    """快速测试一个模型"""
    
    # API配置
    api_key = "sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59"
    url = "https://www.chatfreely.xyz/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    payload = {
        "model": "deepseek-v3-0324",  # 可以修改为其他模型
        "messages": [
            {
                "role": "user",
                "content": "你好！请告诉我今天是星期几，并简单介绍一下你的能力。"
            }
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    print("🚀 开始快速测试...")
    print(f"🤖 测试模型: {payload['model']}")
    print(f"🔑 API Key: {api_key[:15]}...{api_key[-10:]}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 请求成功!")
            
            # 打印响应内容
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"\n🤖 AI回复:\n{content}")
                
                # 打印token使用情况
                if "usage" in data:
                    usage = data["usage"]
                    print(f"\n📊 Token使用情况:")
                    print(f"  输入tokens: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"  输出tokens: {usage.get('completion_tokens', 'N/A')}")
                    print(f"  总计tokens: {usage.get('total_tokens', 'N/A')}")
            
            # 保存完整响应
            with open("quick_test_response.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整响应已保存到 quick_test_response.json")
            
        else:
            print(f"❌ 请求失败!")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
    except Exception as e:
        print(f"💥 未知错误: {e}")

if __name__ == "__main__":
    quick_test()
