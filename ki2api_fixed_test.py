#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ki2API修复版测试脚本
基于诊断结果的修复方案
"""

import requests
import json

def test_ki2api_fixed():
    """测试修复后的Ki2API配置"""
    
    # 可能的正确URL格式
    possible_urls = [
        "https://muskwoled-ki2api.hf.space",  # 原始格式
        "https://huggingface.co/spaces/muskwoled/ki2api",  # Space页面
        # Hugging Face Space的API通常使用这种格式
        "https://muskwoled-ki2api.hf.space",
    ]
    
    api_key = "ki2api-key-2024"
    
    # 不同的认证方式
    auth_methods = [
        {"Authorization": f"Bearer {api_key}"},
        {"Authorization": api_key},
        {"x-api-key": api_key},
        {"api-key": api_key},
        {"X-API-Key": api_key},
    ]
    
    print("🔧 Ki2API修复版测试")
    print("=" * 50)
    
    # 首先测试原始的muskwoled-ki2api.hf.space格式
    base_url = "https://muskwoled-ki2api.hf.space"
    
    print(f"🎯 重点测试: {base_url}")
    print("-" * 30)
    
    # 测试根路径
    try:
        response = requests.get(base_url, timeout=10)
        print(f"根路径状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 根路径正常")
            if "Ki2API" in response.text:
                print("✅ 确认是Ki2API服务")
            print(f"响应内容: {response.text[:300]}")
        else:
            print(f"❌ 根路径失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 根路径异常: {e}")
    
    # 测试不同认证方式的聊天功能
    print(f"\n🔐 测试不同认证方式...")
    
    for i, auth_header in enumerate(auth_methods, 1):
        print(f"\n认证方式 {i}: {auth_header}")
        
        headers = {
            **auth_header,
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "claude-3-5-sonnet-20241022",
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "max_tokens": 50
        }
        
        try:
            chat_url = f"{base_url}/v1/chat/completions"
            response = requests.post(chat_url, headers=headers, json=payload, timeout=20)
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ 成功! 找到正确的认证方式")
                try:
                    data = response.json()
                    if "choices" in data:
                        content = data["choices"][0]["message"]["content"]
                        print(f"  AI回复: {content}")
                    return base_url, auth_header
                except:
                    print(f"  响应: {response.text[:200]}")
            elif response.status_code == 401:
                print(f"  ❌ 认证失败")
            elif response.status_code == 404:
                print(f"  ❌ 端点不存在")
            else:
                print(f"  ❌ 其他错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 测试不同的模型名称
    print(f"\n🤖 测试不同模型名称...")
    
    models_to_test = [
        "claude-3-5-sonnet-20241022",
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307",
        "gpt-4",
        "gpt-3.5-turbo",
        "claude-sonnet-4",
        "claude-4",
        "sonnet"
    ]
    
    # 使用第一种认证方式测试
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    for model in models_to_test:
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": "Hi"}],
            "max_tokens": 20
        }
        
        try:
            response = requests.post(f"{base_url}/v1/chat/completions", 
                                   headers=headers, json=payload, timeout=15)
            print(f"模型 {model}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ 模型 {model} 可用!")
                return base_url, headers, model
            elif response.status_code == 400:
                # 可能是模型名称错误，但端点正确
                try:
                    error_data = response.json()
                    if "model" in response.text.lower():
                        print(f"  ⚠️ 模型名称错误，但端点正确")
                except:
                    pass
                    
        except Exception as e:
            print(f"模型 {model}: 异常 - {e}")
    
    print(f"\n📋 测试总结")
    print("=" * 30)
    print("基于测试结果，可能的问题:")
    print("1. API Key 'ki2api-key-2024' 可能不是有效的密钥")
    print("2. 服务可能需要特殊的认证流程")
    print("3. 模型名称可能与预期不同")
    print("4. 服务可能暂时不可用")
    
    # 最后尝试获取模型列表
    print(f"\n📝 尝试获取模型列表...")
    try:
        models_url = f"{base_url}/v1/models"
        response = requests.get(models_url, headers=headers, timeout=10)
        print(f"模型列表状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 可用模型:")
            for model in data.get('data', []):
                print(f"  - {model.get('id', 'Unknown')}")
        else:
            print(f"模型列表错误: {response.text[:200]}")
    except Exception as e:
        print(f"模型列表异常: {e}")

if __name__ == "__main__":
    test_ki2api_fixed()
