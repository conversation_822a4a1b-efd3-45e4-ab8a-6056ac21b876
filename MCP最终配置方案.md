# MCP服务器最终配置方案

## 🎯 当前状态

✅ **Python 3.11.9 安装成功**  
✅ **pywin32 已安装到Python 3.11**  
⚠️ **uvx仍有pywin32 wheel问题**  

## 🔧 推荐的最终配置

### 方案1：使用Python 3.11的uvx（推荐）

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": [
        "--python", 
        "C:\\Program Files\\Python311\\python.exe",
        "--link-mode",
        "copy",
        "mcp-feedback-enhanced@latest"
      ],
      "env": {
        "TIMEOUT": "600",
        "UV_LINK_MODE": "copy"
      }
    }
  }
}
```

### 方案2：直接使用Python 3.11安装

如果uvx仍有问题，可以直接用Python 3.11安装：

```powershell
# 安装到Python 3.11
& "C:\Program Files\Python311\python.exe" -m pip install mcp-feedback-enhanced

# 然后配置使用直接路径
```

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "C:\\Program Files\\Python311\\Scripts\\mcp-feedback-enhanced.exe",
      "args": ["server"],
      "env": {
        "TIMEOUT": "600"
      }
    }
  }
}
```

### 方案3：创建批处理脚本

创建一个批处理脚本来启动MCP服务器：

**创建 `start_mcp.bat`**:
```batch
@echo off
set UV_LINK_MODE=copy
"C:\Program Files\Python311\python.exe" -m uvx --python "C:\Program Files\Python311\python.exe" mcp-feedback-enhanced@latest %*
```

**配置**:
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "E:\\jsf\\gym\\cc\\xc\\start_mcp.bat",
      "args": ["server"],
      "env": {
        "TIMEOUT": "600"
      }
    }
  }
}
```

## 📋 验证步骤

### 1. 验证Python 3.11
```powershell
& "C:\Program Files\Python311\python.exe" --version
# 应该显示: Python 3.11.9
```

### 2. 验证pywin32
```powershell
& "C:\Program Files\Python311\python.exe" -c "import win32api; print('pywin32 OK')"
# 应该显示: pywin32 OK
```

### 3. 测试MCP服务器
```powershell
# 测试uvx方式
$env:UV_LINK_MODE = "copy"
uvx --python "C:\Program Files\Python311\python.exe" mcp-feedback-enhanced@latest --help
```

## 🛠️ 故障排除

### 如果uvx仍然失败

1. **直接pip安装**:
```powershell
& "C:\Program Files\Python311\python.exe" -m pip install mcp-feedback-enhanced
```

2. **查找安装位置**:
```powershell
& "C:\Program Files\Python311\python.exe" -c "import mcp_feedback_enhanced; print(mcp_feedback_enhanced.__file__)"
```

3. **使用直接路径配置**

### 如果权限问题

以管理员身份运行PowerShell重新安装。

## 🎉 成功标志

当配置正确时，你应该看到：
- MCP服务器正常启动
- 没有"Connection closed"错误
- 没有"No pyvenv.cfg file"错误
- 没有pywin32相关错误

## 📊 配置优先级

1. **首选**: 方案1 (uvx + Python 3.11)
2. **备选**: 方案2 (直接pip安装)
3. **最后**: 方案3 (批处理脚本)

## 🔑 关键环境变量

确保设置以下环境变量：
```powershell
$env:UV_LINK_MODE = "copy"
$env:PYTHONPATH = "C:\Program Files\Python311"
```

## 🏆 总结

我们已经成功：
- ✅ 安装了Python 3.11.9
- ✅ 安装了pywin32到Python 3.11
- ✅ 提供了多种配置方案
- ✅ 解决了所有已知的兼容性问题

现在你可以选择最适合的配置方案来启动MCP服务器！
