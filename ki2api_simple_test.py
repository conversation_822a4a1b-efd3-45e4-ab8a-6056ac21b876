#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ki2API简化测试脚本
测试正确的Hugging Face Space URL
"""

import requests
import json

def test_ki2api():
    """测试Ki2API的正确URL"""
    
    # 正确的Hugging Face Space URL
    base_url = "https://huggingface.co/spaces/muskwoled/ki2api"
    api_key = "ki2api-key-2024"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🚀 Ki2API简化测试")
    print(f"基础URL: {base_url}")
    print(f"API Key: {api_key}")
    print("-" * 50)
    
    # 1. 测试根路径
    print("1️⃣ 测试根路径...")
    try:
        response = requests.get(base_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 根路径正常")
            # 检查是否包含API信息
            if "Ki2API" in response.text:
                print(f"   ✅ 确认是Ki2API服务")
            else:
                print(f"   ⚠️ 可能不是API端点")
        else:
            print(f"   ❌ 根路径失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 根路径异常: {e}")
    
    # 2. 测试健康检查
    print("\n2️⃣ 测试健康检查...")
    try:
        health_url = f"{base_url}/health"
        response = requests.get(health_url, timeout=10)
        print(f"   URL: {health_url}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 健康检查通过")
            print(f"   响应: {response.text[:200]}")
        else:
            print(f"   ❌ 健康检查失败")
            print(f"   响应: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 3. 测试模型列表
    print("\n3️⃣ 测试模型列表...")
    try:
        models_url = f"{base_url}/v1/models"
        response = requests.get(models_url, headers=headers, timeout=10)
        print(f"   URL: {models_url}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 模型列表获取成功")
            try:
                data = response.json()
                print(f"   模型数量: {len(data.get('data', []))}")
            except:
                print(f"   响应: {response.text[:200]}")
        else:
            print(f"   ❌ 模型列表失败")
            print(f"   响应: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ 模型列表异常: {e}")
    
    # 4. 测试聊天功能
    print("\n4️⃣ 测试聊天功能...")
    try:
        chat_url = f"{base_url}/v1/chat/completions"
        payload = {
            "model": "claude-3-5-sonnet-20241022",
            "messages": [
                {"role": "user", "content": "Hello! Just say 'Hi' back."}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        print(f"   URL: {chat_url}")
        print(f"   模型: {payload['model']}")
        
        response = requests.post(chat_url, headers=headers, json=payload, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ 聊天功能正常")
            try:
                data = response.json()
                if "choices" in data and len(data["choices"]) > 0:
                    content = data["choices"][0]["message"]["content"]
                    print(f"   AI回复: {content}")
                else:
                    print(f"   响应格式: {data}")
            except:
                print(f"   响应: {response.text[:200]}")
        else:
            print(f"   ❌ 聊天功能失败")
            print(f"   响应: {response.text[:300]}")
            
    except Exception as e:
        print(f"   ❌ 聊天功能异常: {e}")
    
    # 5. 测试不同的URL格式
    print("\n5️⃣ 测试URL变体...")
    url_variants = [
        "https://muskwoled-ki2api.hf.space",
        "https://muskwoled-ki2api.hf.space/",
        "https://huggingface.co/spaces/muskwoled/ki2api/",
    ]
    
    for url in url_variants:
        try:
            print(f"   测试: {url}")
            response = requests.get(url, timeout=5)
            print(f"     状态码: {response.status_code}")
            if response.status_code == 200 and "Ki2API" in response.text:
                print(f"     ✅ 找到工作的URL!")
                
                # 测试这个URL的聊天功能
                chat_url = f"{url.rstrip('/')}/v1/chat/completions"
                test_payload = {
                    "model": "claude-3-5-sonnet-20241022",
                    "messages": [{"role": "user", "content": "Hi"}],
                    "max_tokens": 20
                }
                
                chat_response = requests.post(chat_url, headers=headers, json=test_payload, timeout=15)
                print(f"     聊天测试状态码: {chat_response.status_code}")
                if chat_response.status_code == 200:
                    print(f"     ✅ 聊天功能也正常!")
                    return url
                    
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    print("如果所有测试都失败，可能的原因:")
    print("1. API Key格式错误")
    print("2. 服务需要特殊的认证方式")
    print("3. 服务暂时不可用")
    print("4. 需要特定的User-Agent或其他请求头")

if __name__ == "__main__":
    test_ki2api()
