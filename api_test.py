#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
测试接口：https://www.chatfreely.xyz/v1/chat/completions
API Key: sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59
"""

import requests
import json
import time
from typing import Dict, List, Any

class APITester:
    def __init__(self):
        self.api_key = "sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59"
        self.base_url = "https://www.chatfreely.xyz/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 支持的模型列表
        self.models = [
            "deepseek-r1-0528",
            "deepseek-v3-0324", 
            "qwen-3-235b-A22b-thinking-2507",
            "qwen-3-235b-a22b-instruct-2507",
            "kimi-k2-instruct"
        ]

    def test_basic_api_call(self, model: str) -> Dict[str, Any]:
        """测试基本API调用"""
        print(f"\n🔍 测试模型: {model}")
        print("-" * 50)
        
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user", 
                    "content": "你好，请简单介绍一下你自己。"
                }
            ],
            "max_tokens": 150,
            "temperature": 0.7
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            end_time = time.time()
            
            result = {
                "model": model,
                "status_code": response.status_code,
                "response_time": round(end_time - start_time, 2),
                "success": False,
                "error": None,
                "response_data": None
            }
            
            if response.status_code == 200:
                data = response.json()
                result["success"] = True
                result["response_data"] = data
                
                print(f"✅ 成功! 响应时间: {result['response_time']}秒")
                if "choices" in data and len(data["choices"]) > 0:
                    content = data["choices"][0]["message"]["content"]
                    print(f"📝 回复内容: {content[:100]}...")
                    
                    # 检查token使用情况
                    if "usage" in data:
                        usage = data["usage"]
                        print(f"🔢 Token使用: 输入={usage.get('prompt_tokens', 'N/A')}, "
                              f"输出={usage.get('completion_tokens', 'N/A')}, "
                              f"总计={usage.get('total_tokens', 'N/A')}")
                
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ 失败! {result['error']}")
                
        except requests.exceptions.Timeout:
            result["error"] = "请求超时"
            print(f"⏰ 请求超时")
        except requests.exceptions.RequestException as e:
            result["error"] = f"请求异常: {str(e)}"
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            result["error"] = f"未知错误: {str(e)}"
            print(f"❌ 未知错误: {e}")
            
        return result

    def test_context_length(self, model: str) -> Dict[str, Any]:
        """测试模型上下文长度"""
        print(f"\n📏 测试 {model} 的上下文长度")
        print("-" * 50)
        
        # 创建一个较长的上下文
        long_context = "这是一个测试上下文长度的消息。" * 100  # 约3000字符
        
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": f"{long_context}\n\n请总结上面的内容，并告诉我你能处理多长的上下文。"
                }
            ],
            "max_tokens": 200,
            "temperature": 0.5
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=45
            )
            end_time = time.time()
            
            result = {
                "model": model,
                "context_test": True,
                "input_length": len(long_context),
                "status_code": response.status_code,
                "response_time": round(end_time - start_time, 2),
                "success": False,
                "error": None
            }
            
            if response.status_code == 200:
                data = response.json()
                result["success"] = True
                print(f"✅ 上下文测试成功! 输入长度: {len(long_context)} 字符")
                print(f"⏱️ 响应时间: {result['response_time']}秒")
                
                if "usage" in data:
                    usage = data["usage"]
                    print(f"🔢 Token使用: {usage.get('total_tokens', 'N/A')}")
                    
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ 上下文测试失败: {result['error']}")
                
        except Exception as e:
            result["error"] = f"上下文测试异常: {str(e)}"
            print(f"❌ 上下文测试异常: {e}")
            
        return result

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试")
        print("=" * 60)
        
        all_results = []
        
        for model in self.models:
            # 基本API测试
            basic_result = self.test_basic_api_call(model)
            all_results.append(basic_result)
            
            # 如果基本测试成功，进行上下文测试
            if basic_result["success"]:
                context_result = self.test_context_length(model)
                all_results.append(context_result)
            
            time.sleep(1)  # 避免请求过于频繁
        
        # 生成测试报告
        self.generate_report(all_results)
        
        return all_results

    def generate_report(self, results: List[Dict[str, Any]]):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        successful_models = []
        failed_models = []
        
        for result in results:
            if result["success"]:
                successful_models.append(result["model"])
            else:
                failed_models.append((result["model"], result.get("error", "未知错误")))
        
        print(f"\n✅ 成功的模型 ({len(successful_models)}):")
        for model in set(successful_models):
            print(f"  - {model}")
        
        print(f"\n❌ 失败的模型 ({len(failed_models)}):")
        for model, error in failed_models:
            print(f"  - {model}: {error}")
        
        # 保存详细结果到文件
        with open("test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到 test_results.json")

def main():
    """主函数"""
    tester = APITester()
    
    print("🔑 API Key:", tester.api_key[:20] + "..." + tester.api_key[-10:])
    print("🌐 API地址:", tester.base_url)
    print("🤖 支持的模型:", ", ".join(tester.models))
    
    try:
        results = tester.run_all_tests()
        print("\n🎉 测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
