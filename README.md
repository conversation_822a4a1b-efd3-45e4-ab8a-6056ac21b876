# API测试脚本

这个项目包含了用于测试 ChatFreely API 的脚本。

## API信息

- **接口地址**: https://www.chatfreely.xyz/v1/chat/completions
- **API Key**: sk-df1b31ae1aa8115212fcb99ddaffdd21270e9ba348a109250f4a302f5bb8be59

## 支持的模型

- deepseek-r1-0528
- deepseek-v3-0324
- qwen-3-235b-A22b-thinking-2507
- qwen-3-235b-a22b-instruct-2507
- kimi-k2-instruct

## 文件说明

### 1. `api_test.py` - 完整测试脚本
功能：
- 测试所有支持的模型
- 基本API调用测试
- 上下文长度测试
- 生成详细的测试报告

### 2. `simple_test.py` - 快速测试脚本
功能：
- 快速测试单个模型
- 简单的API调用验证
- 适合快速验证API是否正常工作

### 3. `requirements.txt` - 依赖文件
包含所需的Python包

## 使用方法

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行完整测试
```bash
python api_test.py
```

### 运行快速测试
```bash
python simple_test.py
```

## 测试内容

### 1. 基本API调用测试
- 验证API密钥是否有效
- 测试每个模型的基本响应
- 检查响应时间和token使用情况

### 2. 上下文长度测试
- 发送较长的输入文本
- 测试模型处理长上下文的能力
- 验证token计算是否正确

## 输出文件

- `test_results.json` - 完整测试的详细结果
- `quick_test_response.json` - 快速测试的响应数据

## 注意事项

1. 请确保网络连接正常
2. API调用可能需要一些时间，请耐心等待
3. 如果某个模型测试失败，脚本会继续测试其他模型
4. 测试结果会保存到JSON文件中，方便后续分析

## 自定义测试

你可以修改脚本中的以下参数：
- `max_tokens`: 最大输出token数
- `temperature`: 生成文本的随机性
- 测试消息内容
- 要测试的模型列表
